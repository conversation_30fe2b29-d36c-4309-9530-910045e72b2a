import { SystemSettingsState } from '@/types'
import logger from '@/lib/utils/logger';
import { fetchWithCorrectUrl } from '@/lib/utils/url';

export async function getSystemSettings(): Promise<SystemSettingsState> {
  try {
    const response = await fetchWithCorrectUrl('/api/settings')
    if (!response.ok) throw new Error('获取系统设置失败')
    return await response.json()
  } catch (error) {
    logger.error('获取系统设置失败:', error)
    throw error
  }
}

export async function updateSystemSettings(settings: SystemSettingsState): Promise<void> {
  try {
    const response = await fetchWithCorrectUrl('/api/settings', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings),
    })

    if (!response.ok) throw new Error('更新系统设置失败')
  } catch (error) {
    logger.error('更新系统设置失败:', error)
    throw error
  }
}

export async function uploadLogo(file: File): Promise<string> {
  try {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetchWithCorrectUrl('/api/upload/logo', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) throw new Error('上传logo失败')
    return await response.json()
  } catch (error) {
    logger.error('上传logo失败:', error)
    throw error
  }
}

export async function uploadBackgroundImage(file: File): Promise<string> {
  try {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetchWithCorrectUrl('/api/upload/image', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) throw new Error('上传背景图片失败')
    return await response.json()
  } catch (error) {
    logger.error('上传背景图片失败:', error)
    throw error
  }
}
