/**
 * URL 工具函数
 * 用于处理服务端和客户端的 URL 解析问题
 */

import { SERVER_APP_URL, APP_URL } from '@/lib/config';

/**
 * 获取完整的 API URL
 * 在服务端使用 SERVER_APP_URL，在客户端使用相对路径
 * 
 * @param path API 路径，如 '/api/settings'
 * @returns 完整的 URL
 */
export function getApiUrl(path: string): string {
  // 如果路径已经是完整的 URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }

  // 确保路径以 / 开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;

  // 在服务端环境中，使用完整的 URL
  if (typeof window === 'undefined') {
    return `${SERVER_APP_URL}${normalizedPath}`;
  }

  // 在客户端环境中，使用相对路径
  return normalizedPath;
}

/**
 * 获取完整的应用 URL
 * 
 * @param path 应用路径，如 '/dashboard'
 * @returns 完整的 URL
 */
export function getAppUrl(path: string = ''): string {
  // 如果路径已经是完整的 URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }

  // 确保路径以 / 开头（除非是空字符串）
  const normalizedPath = path && !path.startsWith('/') ? `/${path}` : path;

  // 在服务端环境中，使用 SERVER_APP_URL
  if (typeof window === 'undefined') {
    return `${SERVER_APP_URL}${normalizedPath}`;
  }

  // 在客户端环境中，使用 APP_URL
  return `${APP_URL}${normalizedPath}`;
}

/**
 * 创建一个支持服务端和客户端的 fetch 函数
 * 
 * @param input 请求的 URL 或 Request 对象
 * @param init 请求选项
 * @returns Promise<Response>
 */
export function fetchWithCorrectUrl(
  input: RequestInfo | URL,
  init?: RequestInit
): Promise<Response> {
  let url: string;

  if (typeof input === 'string') {
    url = getApiUrl(input);
  } else if (input instanceof URL) {
    url = input.toString();
  } else {
    // Request 对象
    url = getApiUrl(input.url);
  }

  return fetch(url, init);
}

/**
 * 检查当前是否在服务端环境
 */
export function isServerSide(): boolean {
  return typeof window === 'undefined';
}

/**
 * 检查当前是否在客户端环境
 */
export function isClientSide(): boolean {
  return typeof window !== 'undefined';
}
